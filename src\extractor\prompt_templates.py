"""
問答生成提示詞模板
包含各種不同類型的提示詞模板，用於生成高品質的占星學問答對
"""

from typing import Dict, List


class PromptTemplates:
    """提示詞模板管理器"""
    
    def __init__(self):
        """初始化提示詞模板"""
        self.templates = {
            'basic': self._basic_template(),
            'detailed': self._detailed_template(),
            'conceptual': self._conceptual_template(),
            'practical': self._practical_template(),
            'analytical': self._analytical_template()
        }
    
    def get_template(self, template_name: str) -> str:
        """獲取指定的提示詞模板"""
        return self.templates.get(template_name, self.templates['basic'])
    
    def get_available_templates(self) -> List[str]:
        """獲取可用的模板列表"""
        return list(self.templates.keys())
    
    def _basic_template(self) -> str:
        """基礎提示詞模板"""
        return """
你是一位專業的占星學教育專家。請根據以下占星學內容生成 {num_questions} 個占星學基礎知識問題與參考資料對。

內容：
{text_chunk}

要求：
1. 問題應該聚焦於占星學知識本身，而非特定書籍
2. 問題類型：基礎概念、核心原理、基本應用
3. 避免使用「本書如何...」、「作者在書中...」等表述
4. 改為詢問占星學概念、原理、應用等通用知識
5. **重要條件：如果提供的內容與占星學關聯性很小或無關，請將 question 設為空字串 ""**
6. 參考資料要求：
   - 基於提供的原始文本內容進行整理
   - 保持內容的完整性和準確性
   - 避免重複相同的段落或句子
   - 確保邏輯清晰、結構完整
   - 長度適中，包含回答問題所需的關鍵資訊
7. 使用繁體中文

問題範例：
- 什麼是占星學中的黃道十二宮？
- 行星在占星學中代表什麼意義？
- 如何解讀星盤中的相位關係？

請以以下 JSON 格式回答：
[
    {{"question": "占星學知識問題", "resource": "整理後的參考資料"}}
]
"""
    
    def _detailed_template(self) -> str:
        """詳細提示詞模板"""
        return """
你是一位資深的占星學教育專家，擁有豐富的教學經驗。請根據以下占星學內容生成 {num_questions} 個占星學深度分析問題與參考資料對。

內容：
{text_chunk}

生成要求：
1. 問題設計原則：
   - 聚焦於占星學知識本身，而非特定書籍
   - 涵蓋關鍵概念和重要細節
   - 包含不同認知層次（記憶、理解、應用、分析）
   - 避免使用「本書如何...」、「作者在書中...」等表述
   - 改為詢問占星學的深層原理、複雜概念、進階應用
   - 確保問題具有教育價值

2. 參考資料品質標準：
   - 基於提供的內容，準確整理
   - 結構清晰，邏輯性強
   - 避免重複相同的段落或句子
   - 包含必要的背景說明，但不冗餘
   - 適合占星學學習者理解
   - 每個問題的參考資料應該是獨特的，針對該問題的相關內容

3. 問題類型分布：
   - 事實性問題：基本概念、定義、特徵
   - 理解性問題：原理、關係、影響
   - 應用性問題：實際運用、案例分析

4. 語言要求：
   - 使用繁體中文
   - 專業術語使用準確
   - 表達清晰易懂

請以 JSON 格式回答：
[
    {{"question": "問題內容", "resource": "整理後的參考資料", "type": "問題類型"}},
    ...
]
"""
    
    def _conceptual_template(self) -> str:
        """概念性提示詞模板"""
        return """
作為占星學概念教學專家，請根據以下內容生成 {num_questions} 個占星學概念理解問題與參考資料對。

內容：
{text_chunk}

重點關注：
1. 核心概念的定義和特徵
2. 概念之間的關係和區別
3. 概念的實際意義和應用
4. 常見的誤解和澄清

問題設計要求：
- 聚焦於占星學概念本身，而非特定書籍
- 幫助學習者深入理解占星學概念
- 促進批判性思考
- 連結理論與實踐
- 避免使用「本書如何...」、「作者在書中...」等表述
- 改為詢問占星學概念的定義、特徵、關係等
- 使用繁體中文

問題範例：
- 占星學中的「相位」概念是什麼？有哪些主要類型？
- 本命盤與流年盤在概念上有什麼區別？
- 如何理解占星學中「宮位」與「星座」的概念差異？

JSON 格式回答：
[
    {{"question": "概念問題", "resource": "整理後的概念說明資料", "concept": "主要概念"}}
]
"""
    
    def _practical_template(self) -> str:
        """實用性提示詞模板"""
        return """
你是一位實務導向的占星學導師。請根據以下內容生成 {num_questions} 個占星學實用應用問題與參考資料對。

內容：
{text_chunk}

實用性重點：
1. 如何在實際占星諮詢中應用
2. 具體的操作方法和步驟
3. 實際案例和情境
4. 常見問題的解決方案

問題與資料要求：
- 聚焦於占星學實用技能，而非特定書籍
- 問題貼近實際應用場景
- 避免使用「本書如何...」、「作者在書中...」等表述
- 改為詢問占星學的實際應用方法、技巧、步驟等
- 參考資料提供具體可行的指導
- 包含實用技巧和注意事項
- 繁體中文表達

問題範例：
- 如何在星盤解讀中分析一個人的事業發展潛力？
- 占星師在進行關係諮詢時應該注意哪些要點？
- 如何運用流年技巧來預測重要的人生轉折點？

JSON 格式：
[
    {{"question": "實用問題", "resource": "整理後的實用指導資料", "application": "應用場景"}},
    ...
]
"""
    
    def _analytical_template(self) -> str:
        """分析性提示詞模板"""
        return """
作為占星學分析專家，請根據以下內容生成 {num_questions} 個分析性問題與參考資料對。

文獻：{book_title} - {chapter_title}

內容：
{text_chunk}

分析重點：
1. 深層原理和機制
2. 因果關係和影響因素
3. 比較分析和對比
4. 批判性評估

問題特色：
- 需要深度思考和分析
- 涉及多層面的考量
- 促進邏輯推理能力
- 培養批判性思維

參考資料要求：
- 多角度整理
- 邏輯清晰
- 有理有據
- 繁體中文

JSON 格式：
[
    {{"question": "分析問題", "resource": "整理後的分析資料", "analysis_type": "分析類型"}},
    ...
]
"""


def main():
    """測試函數"""
    templates = PromptTemplates()
    
    print("📝 可用的提示詞模板:")
    for template_name in templates.get_available_templates():
        print(f"  - {template_name}")
    
    print(f"\n🔍 基礎模板範例:")
    basic_template = templates.get_template('basic')
    
    # 測試變數替換
    test_variables = {
        'num_questions': 2,
        'book_title': '占星學基礎',
        'chapter_title': '十二星座',
        'text_chunk': '牡羊座是黃道十二宮的第一個星座...'
    }
    
    formatted_prompt = basic_template.format(**test_variables)
    print(formatted_prompt[:300] + "...")


if __name__ == "__main__":
    main()
