#!/usr/bin/env python3
"""
多線程從問題與參考資料，思考對，進行思考優化

使用多線程同步處理多本書
"""

import json
import sys
import time
from pathlib import Path
from typing import List, Dict, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# 添加 src 目錄到路徑
sys.path.append(str(Path(__file__).parent.parent))
from utils.logger import get_logger

# 導入我們的模組
from openai_client import AzureOpenAIClient
from prompt_templates import PromptTemplates


class MultiThreadEbookQAGenerator:
    """多線程電子書問題與參考資料生成器"""
    
    def __init__(self):
        """初始化生成器"""
        self.logger = get_logger(__name__)
        
        # 線程鎖，用於統計資訊
        self.stats_lock = threading.Lock()
        
        # 統計資訊
        self.stats = {
            'total_books': 0,
            'processed_books': 0,
            'failed_books': 0,
            'total_chunks': 0,
            'total_qa_pairs': 0,
            'api_calls': 0,
            'api_errors': 0
        }
        
        self.logger.info("多線程電子書問題生成器初始化完成")
    
    def find_real_ebook_files(self, ebooks_dir: Path) -> List[Path]:
        """尋找所有真實電子書檔案，每本書只選擇一個最佳格式"""
        ebook_files = []
        
        if not ebooks_dir.exists():
            self.logger.error(f"電子書目錄不存在: {ebooks_dir}")
            return []
        
        # 格式優先順序：PDF > EPUB > 其他
        format_priority = {'.pdf': 1, '.epub': 2, '.mobi': 3, '.azw': 4, '.azw3': 5}
        
        # 遍歷所有書籍目錄
        for book_dir in ebooks_dir.iterdir():
            if book_dir.is_dir():
                book_id = book_dir.name
                
                # 收集該書籍目錄中的所有電子書檔案
                available_files = []
                for file in book_dir.glob("*"):
                    if file.suffix.lower() in format_priority and file.is_file():
                        # 檢查檔案大小，排除太小的檔案（可能是損壞的）
                        if file.stat().st_size > 1000:  # 至少 1KB
                            available_files.append(file)
                
                if available_files:
                    # 根據優先順序選擇最佳格式
                    best_file = min(available_files, 
                                  key=lambda f: format_priority.get(f.suffix.lower(), 99))
                    ebook_files.append(best_file)
                    self.logger.info(f"書籍 {book_id}: 選擇 {best_file.name} (格式: {best_file.suffix})")
                else:
                    self.logger.warning(f"書籍目錄 {book_id} 中沒有找到有效的電子書檔案")
        
        self.logger.info(f"總共找到 {len(ebook_files)} 本真實電子書")
        return ebook_files
    

    
    def save_qa_pairs_jsonl(self, qa_pairs: List[Dict], output_file: Path) -> None:
        """保存問題與參考資料對為 JSONL 格式"""
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            for qa_pair in qa_pairs:
                json.dump(qa_pair, f, ensure_ascii=False)
                f.write('\n')
    
    def generate_from_all_ebooks(self, 
                                ebooks_dir: Path, 
                                output_dir: Path,
                                max_workers: int = 3,
                                max_books: Optional[int] = None) -> None:
        """使用多線程從所有電子書生成問題與參考資料對"""
        
        self.logger.info("開始多線程從真實電子書生成問題與參考資料對...")
        
        # 跳過連接測試，直接開始處理
        self.logger.info("跳過 Azure OpenAI API 連接測試，直接開始處理...")
        
        # 尋找真實電子書檔案
        ebook_files = self.find_real_ebook_files(ebooks_dir)
        
        if not ebook_files:
            self.logger.error("沒有找到任何真實電子書檔案")
            return
        
        if max_books:
            ebook_files = ebook_files[:max_books]
            self.logger.info(f"限制處理 {max_books} 本書")
        
        self.stats['total_books'] = len(ebook_files)
        
        # 使用線程池處理書籍
        self.logger.info(f"使用 {max_workers} 個線程同時處理 {len(ebook_files)} 本書")
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任務
            future_to_book = {
                executor.submit(self.process_single_ebook, ebook_file, output_dir): ebook_file 
                for ebook_file in ebook_files
            }
            
            # 處理完成的任務
            completed_count = 0
            for future in as_completed(future_to_book):
                ebook_file = future_to_book[future]
                completed_count += 1
                
                try:
                    result = future.result()
                    if result['success']:
                        self.logger.info(f"[{completed_count}/{len(ebook_files)}] ✅ {result['book_id']}: {result['qa_count']} 個問題")
                    else:
                        self.logger.error(f"[{completed_count}/{len(ebook_files)}] ❌ {result['book_id']}: {result['error']}")
                except Exception as e:
                    self.logger.error(f"[{completed_count}/{len(ebook_files)}] ❌ {ebook_file.name}: {e}")
        
        # 保存統計資訊
        stats_file = output_dir / "generation_stats_multithread.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(self.stats, f, ensure_ascii=False, indent=2)
        
        self.logger.info("多線程問題與參考資料生成完成！")
        self.print_stats()
    
    def print_stats(self) -> None:
        """顯示統計資訊"""
        print("\n📊 多線程生成統計:")
        print("=" * 50)
        print(f"📚 總書籍數: {self.stats['total_books']}")
        print(f"✅ 成功處理: {self.stats['processed_books']}")
        print(f"❌ 處理失敗: {self.stats['failed_books']}")
        print(f"📄 總文本塊: {self.stats['total_chunks']}")
        print(f"❓ 總問題對: {self.stats['total_qa_pairs']}")
        print(f"🔗 API 呼叫: {self.stats['api_calls']}")
        print(f"⚠️  API 錯誤: {self.stats['api_errors']}")
        
        if self.stats['processed_books'] > 0:
            avg_qa_per_book = self.stats['total_qa_pairs'] / self.stats['processed_books']
            avg_chunks_per_book = self.stats['total_chunks'] / self.stats['processed_books']
            print(f"📈 平均每本書: {avg_qa_per_book:.1f} 個問題對")
            print(f"📄 平均每本書: {avg_chunks_per_book:.1f} 個文本塊")
        
        if self.stats['api_calls'] > 0:
            success_rate = (self.stats['api_calls'] - self.stats['api_errors']) / self.stats['api_calls'] * 100
            print(f"🎯 API 成功率: {success_rate:.1f}%")


def main():
    """主函數"""
    print("🚀 開始多線程從真實電子書生成問題與參考資料對...")
    print("=" * 60)
    
    generator = MultiThreadEbookQAGenerator()
    
    # 設定路徑
    ebooks_dir = Path("../../data/raw/ebooks")
    output_dir = Path("../../data/processed/step1_ebook_question")
    
    # 設定參數
    max_workers = 3  # 同時處理的線程數
    max_books = None  # 處理全部 36 本電子書
    
    print(f"📖 電子書目錄: {ebooks_dir}")
    print(f"💾 輸出目錄: {output_dir}")
    print(f"🧵 線程數: {max_workers}")
    print(f"📊 處理書籍: {max_books if max_books else '全部 36 本'}")
    print(f"📄 處理範圍: 每本書的全部文本塊")
    print(f"🔢 每個文本塊生成: 4 個問題 (每種模板 1 個)")
    print(f"📁 輸出格式: 每本書一個獨立的 JSONL 檔案")
    
    # 檢查目錄是否存在
    if not ebooks_dir.exists():
        print(f"❌ 電子書目錄不存在: {ebooks_dir}")
        return
    
    # 開始生成
    generator.generate_from_all_ebooks(
        ebooks_dir=ebooks_dir,
        output_dir=output_dir,
        max_workers=max_workers,
        max_books=max_books
    )
    
    print("\n🎉 任務完成！")


if __name__ == "__main__":
    main()
