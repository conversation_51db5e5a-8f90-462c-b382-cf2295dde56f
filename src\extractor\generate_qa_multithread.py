#!/usr/bin/env python3
"""
多線程從真實電子書生成問題與參考資料對
- 每個文本塊都生成問題（使用4種模板）
- 處理整本書的全部文本塊
- 每本書生成一個獨立的問題集檔案
- 使用多線程同步處理多本書
"""

import json
import sys
import time
from pathlib import Path
from typing import List, Dict, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import random

# 添加 src 目錄到路徑
sys.path.append(str(Path(__file__).parent.parent))
from utils.logger import get_logger

# 導入我們的模組
from ebook_parser import EbookParser
from text_splitter import SmartTextSplitter
from openai_client import AzureOpenAIClient
from prompt_templates import PromptTemplates


class MultiThreadEbookQAGenerator:
    """多線程電子書問題與參考資料生成器"""
    
    def __init__(self):
        """初始化生成器"""
        self.logger = get_logger(__name__)
        
        # 線程鎖，用於統計資訊
        self.stats_lock = threading.Lock()
        
        # 統計資訊
        self.stats = {
            'total_books': 0,
            'processed_books': 0,
            'failed_books': 0,
            'total_chunks': 0,
            'total_qa_pairs': 0,
            'api_calls': 0,
            'api_errors': 0
        }
        
        self.logger.info("多線程電子書問題生成器初始化完成")
    
    def find_real_ebook_files(self, ebooks_dir: Path) -> List[Path]:
        """尋找所有真實電子書檔案，每本書只選擇一個最佳格式"""
        ebook_files = []
        
        if not ebooks_dir.exists():
            self.logger.error(f"電子書目錄不存在: {ebooks_dir}")
            return []
        
        # 格式優先順序：PDF > EPUB > 其他
        format_priority = {'.pdf': 1, '.epub': 2, '.mobi': 3, '.azw': 4, '.azw3': 5}
        
        # 遍歷所有書籍目錄
        for book_dir in ebooks_dir.iterdir():
            if book_dir.is_dir():
                book_id = book_dir.name
                
                # 收集該書籍目錄中的所有電子書檔案
                available_files = []
                for file in book_dir.glob("*"):
                    if file.suffix.lower() in format_priority and file.is_file():
                        # 檢查檔案大小，排除太小的檔案（可能是損壞的）
                        if file.stat().st_size > 1000:  # 至少 1KB
                            available_files.append(file)
                
                if available_files:
                    # 根據優先順序選擇最佳格式
                    best_file = min(available_files, 
                                  key=lambda f: format_priority.get(f.suffix.lower(), 99))
                    ebook_files.append(best_file)
                    self.logger.info(f"書籍 {book_id}: 選擇 {best_file.name} (格式: {best_file.suffix})")
                else:
                    self.logger.warning(f"書籍目錄 {book_id} 中沒有找到有效的電子書檔案")
        
        self.logger.info(f"總共找到 {len(ebook_files)} 本真實電子書")
        return ebook_files
    
    def process_single_ebook(self, ebook_file: Path, output_dir: Path) -> Dict:
        """處理單本真實電子書（線程安全）"""
        book_id = ebook_file.parent.name
        thread_id = threading.current_thread().ident
        
        # 為每個線程建立獨立的組件實例
        parser = EbookParser()
        splitter = SmartTextSplitter(chunk_size=1000, chunk_overlap=100)
        openai_client = AzureOpenAIClient()
        
        self.logger.info(f"[線程 {thread_id}] 開始處理書籍: {book_id} - {ebook_file.name}")
        
        try:
            # 解析真實電子書
            book_data = parser.parse_file(ebook_file)
            
            if not book_data or not book_data.get('full_text'):
                self.logger.warning(f"[線程 {thread_id}] 書籍 {book_id} 解析失敗或內容為空")
                return {'success': False, 'book_id': book_id, 'error': '解析失敗'}
            
            # 準備書籍元數據
            book_metadata = {
                'book_id': book_id,
                'book_title': book_data.get('title', ebook_file.stem),
                'book_format': book_data.get('format', ebook_file.suffix),
                'file_path': str(ebook_file)
            }
            
            # 分割文本
            chunks = splitter.split_book(book_data)
            
            if not chunks:
                self.logger.warning(f"[線程 {thread_id}] 書籍 {book_id} 沒有可處理的文本塊")
                return {'success': False, 'book_id': book_id, 'error': '無文本塊'}
            
            self.logger.info(f"[線程 {thread_id}] 書籍 {book_id} 分割為 {len(chunks)} 個文本塊")
            
            # 生成問題與參考資料對
            all_qa_pairs = []
            
            # 定義可用的模板
            available_templates = ['conceptual', 'practical']

            # 處理每個文本塊
            for i, chunk in enumerate(chunks, 1):
                self.logger.info(f"[線程 {thread_id}] 處理文本塊 {i}/{len(chunks)} (長度: {len(chunk['content'])} 字元)")

                # 隨機選擇一個模板生成問題
                template_name = random.choice(available_templates)
                self.logger.info(f"[線程 {thread_id}] 隨機選擇模板: {template_name}")

                try:
                    # 準備上下文
                    context = {
                        'book_id': book_id,
                        'book_title': book_metadata['book_title'],
                        'chapter_title': chunk['metadata'].get('chapter_title', ''),
                        'template_name': template_name,
                        'chunk_index': chunk['chunk_id']
                    }

                    # 生成問題與參考資料對
                    qa_pairs = openai_client.generate_question_reference_pairs(
                        chunk['content'],
                        context,
                        template_name=template_name,
                        num_questions=1  # 每個模板生成 1 個問題
                    )

                    # 更新統計（線程安全）
                    with self.stats_lock:
                        self.stats['api_calls'] += 1

                    if qa_pairs:
                        # 修改 source 欄位為書名
                        for qa_pair in qa_pairs:
                            qa_pair['source'] = book_metadata['book_title']
                            qa_pair['template_used'] = template_name

                        all_qa_pairs.extend(qa_pairs)

                        # 更新統計（線程安全）
                        with self.stats_lock:
                            self.stats['total_qa_pairs'] += len(qa_pairs)

                    # 避免 API 速率限制
                    time.sleep(0.5)

                except Exception as e:
                    self.logger.error(f"[線程 {thread_id}] 使用模板 {template_name} 處理文本塊 {i} 時發生錯誤: {e}")
                    with self.stats_lock:
                        self.stats['api_errors'] += 1
                    continue
            
            # 保存該書籍的問題集
            if all_qa_pairs:
                book_output_file = output_dir / f"qa_pairs_{book_id}.jsonl"
                self.save_qa_pairs_jsonl(all_qa_pairs, book_output_file)
                
                # 更新統計（線程安全）
                with self.stats_lock:
                    self.stats['total_chunks'] += len(chunks)
                    self.stats['processed_books'] += 1
                
                self.logger.info(f"[線程 {thread_id}] 書籍 {book_id} 處理完成，生成 {len(all_qa_pairs)} 個問題與參考資料對")
                return {
                    'success': True, 
                    'book_id': book_id, 
                    'qa_count': len(all_qa_pairs),
                    'chunk_count': len(chunks),
                    'output_file': str(book_output_file)
                }
            else:
                with self.stats_lock:
                    self.stats['failed_books'] += 1
                return {'success': False, 'book_id': book_id, 'error': '沒有生成任何問題'}
            
        except Exception as e:
            self.logger.error(f"[線程 {thread_id}] 處理書籍 {book_id} 時發生錯誤: {e}")
            with self.stats_lock:
                self.stats['failed_books'] += 1
            return {'success': False, 'book_id': book_id, 'error': str(e)}
    
    def save_qa_pairs_jsonl(self, qa_pairs: List[Dict], output_file: Path) -> None:
        """保存問題與參考資料對為 JSONL 格式"""
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            for qa_pair in qa_pairs:
                json.dump(qa_pair, f, ensure_ascii=False)
                f.write('\n')
    
    def generate_from_all_ebooks(self, 
                                ebooks_dir: Path, 
                                output_dir: Path,
                                max_workers: int = 5,
                                max_books: Optional[int] = None) -> None:
        """使用多線程從所有電子書生成問題與參考資料對"""
        
        self.logger.info("開始多線程從真實電子書生成問題與參考資料對...")
        
        # 跳過連接測試，直接開始處理
        self.logger.info("跳過 Azure OpenAI API 連接測試，直接開始處理...")
        
        # 尋找真實電子書檔案
        ebook_files = self.find_real_ebook_files(ebooks_dir)
        
        if not ebook_files:
            self.logger.error("沒有找到任何真實電子書檔案")
            return
        
        if max_books:
            ebook_files = ebook_files[:max_books]
            self.logger.info(f"限制處理 {max_books} 本書")
        
        self.stats['total_books'] = len(ebook_files)
        
        # 使用線程池處理書籍
        self.logger.info(f"使用 {max_workers} 個線程同時處理 {len(ebook_files)} 本書")
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任務
            future_to_book = {
                executor.submit(self.process_single_ebook, ebook_file, output_dir): ebook_file 
                for ebook_file in ebook_files
            }
            
            # 處理完成的任務
            completed_count = 0
            for future in as_completed(future_to_book):
                ebook_file = future_to_book[future]
                completed_count += 1
                
                try:
                    result = future.result()
                    if result['success']:
                        self.logger.info(f"[{completed_count}/{len(ebook_files)}] ✅ {result['book_id']}: {result['qa_count']} 個問題")
                    else:
                        self.logger.error(f"[{completed_count}/{len(ebook_files)}] ❌ {result['book_id']}: {result['error']}")
                except Exception as e:
                    self.logger.error(f"[{completed_count}/{len(ebook_files)}] ❌ {ebook_file.name}: {e}")
        
        # 保存統計資訊
        stats_file = output_dir / "generation_stats_multithread.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(self.stats, f, ensure_ascii=False, indent=2)
        
        self.logger.info("多線程問題與參考資料生成完成！")
        self.print_stats()
    
    def print_stats(self) -> None:
        """顯示統計資訊"""
        print("\n📊 多線程生成統計:")
        print("=" * 50)
        print(f"📚 總書籍數: {self.stats['total_books']}")
        print(f"✅ 成功處理: {self.stats['processed_books']}")
        print(f"❌ 處理失敗: {self.stats['failed_books']}")
        print(f"📄 總文本塊: {self.stats['total_chunks']}")
        print(f"❓ 總問題對: {self.stats['total_qa_pairs']}")
        print(f"🔗 API 呼叫: {self.stats['api_calls']}")
        print(f"⚠️  API 錯誤: {self.stats['api_errors']}")
        
        if self.stats['processed_books'] > 0:
            avg_qa_per_book = self.stats['total_qa_pairs'] / self.stats['processed_books']
            avg_chunks_per_book = self.stats['total_chunks'] / self.stats['processed_books']
            print(f"📈 平均每本書: {avg_qa_per_book:.1f} 個問題對")
            print(f"📄 平均每本書: {avg_chunks_per_book:.1f} 個文本塊")
        
        if self.stats['api_calls'] > 0:
            success_rate = (self.stats['api_calls'] - self.stats['api_errors']) / self.stats['api_calls'] * 100
            print(f"🎯 API 成功率: {success_rate:.1f}%")


def main():
    """主函數"""
    print("🚀 開始多線程從真實電子書生成問題與參考資料對...")
    print("=" * 60)
    
    generator = MultiThreadEbookQAGenerator()
    
    # 設定路徑
    ebooks_dir = Path("../../data/raw/ebooks")
    output_dir = Path("../../data/processed/step1_ebook_question")
    
    # 設定參數
    max_workers = 3  # 同時處理的線程數
    max_books = None  # 處理全部 36 本電子書
    
    print(f"📖 電子書目錄: {ebooks_dir}")
    print(f"💾 輸出目錄: {output_dir}")
    print(f"🧵 線程數: {max_workers}")
    print(f"📊 處理書籍: {max_books if max_books else '全部 36 本'}")
    print(f"📄 處理範圍: 每本書的全部文本塊")
    print(f"🔢 每個文本塊生成: 4 個問題 (每種模板 1 個)")
    print(f"📁 輸出格式: 每本書一個獨立的 JSONL 檔案")
    
    # 檢查目錄是否存在
    if not ebooks_dir.exists():
        print(f"❌ 電子書目錄不存在: {ebooks_dir}")
        return
    
    # 開始生成
    generator.generate_from_all_ebooks(
        ebooks_dir=ebooks_dir,
        output_dir=output_dir,
        max_workers=max_workers,
        max_books=max_books
    )
    
    print("\n🎉 任務完成！")


if __name__ == "__main__":
    main()
